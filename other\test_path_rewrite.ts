/**
 * Test script for HTML path rewriting functionality
 */

// Copy the rewriting function for testing
const PROXY_PREFIX = "/proxy/";

function rewriteHtmlPaths(html: string, targetOrigin: string, proxyOrigin: string): string {
  // Fast path: if no relative paths found, return original
  if (!html.includes('href="/') && !html.includes('src="/') && !html.includes("href='/") && !html.includes("src='/")) {
    return html;
  }

  // Rewrite relative paths to absolute proxy paths
  return html
    .replace(/href=["']\/([^"']*?)["']/g, `href="${proxyOrigin}/${PROXY_PREFIX.slice(1)}${targetOrigin}/$1"`)
    .replace(/src=["']\/([^"']*?)["']/g, `src="${proxyOrigin}/${PROXY_PREFIX.slice(1)}${targetOrigin}/$1"`);
}

// Test case
const testHtml = `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/assets/logo-Bvc5MQ_X.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GPT Load</title>
    <script type="module" crossorigin src="/assets/index-CbzMg2QY.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-DlKHxuit.css">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>`;

const targetOrigin = "https://x.y.com";
const proxyOrigin = "https://proxy.x.com";

const result = rewriteHtmlPaths(testHtml, targetOrigin, proxyOrigin);
console.log("Original HTML:");
console.log(testHtml);
console.log("\nRewritten HTML:");
console.log(result);

// Expected result should have paths like:
// href="https://proxy.x.com/proxy/https://x.y.com/assets/logo-Bvc5MQ_X.png"
// src="https://proxy.x.com/proxy/https://x.y.com/assets/index-CbzMg2QY.js"
